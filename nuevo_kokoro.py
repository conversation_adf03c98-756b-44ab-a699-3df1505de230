import torch
from transformers import VitsModel, AutoTokenizer
from safetensors.torch import load_file
import scipy.io.wavfile as wav

# Rutas
BASE_MODEL = "ylacombe/mms-tts-spa-train"   # modelo base
CHECKPOINT_DIR = "/media/steve/Nuevo vol1/Andrea_voz/backup_modelo_150_epocas"  # carpeta donde está model.safetensors
OUTPUT_WAV = "salida.wav"

# 1. Cargar modelo base y tokenizer
model = VitsModel.from_pretrained(BASE_MODEL)
tokenizer = AutoTokenizer.from_pretrained(BASE_MODEL)

# 2. Cargar pesos fine-tuneados
checkpoint = load_file(f"{CHECKPOINT_DIR}/model.safetensors")
model.load_state_dict(checkpoint, strict=False)

# 3. Mandar el modelo a GPU si está disponible
device = "cuda" if torch.cuda.is_available() else "cpu"
model = model.to(device)

# 4. Texto de prueba
texto = "hola muy buenas tardes, le habla yatsuri yamilet de la mesa de servicios de solla, en que puedo ayudarle"

# Tokenizar
inputs = tokenizer(text=texto, return_tensors="pt").to(device)

# 5. Generar audio
with torch.no_grad():
    output = model(**inputs)
    audio = output.waveform.squeeze().cpu().numpy()

# 6. Guardar a WAV (16kHz por defecto en MMS/VITS)
wav.write(OUTPUT_WAV, rate=model.config.sampling_rate, data=audio)

print(f"✅ Audio generado en {OUTPUT_WAV}")
 