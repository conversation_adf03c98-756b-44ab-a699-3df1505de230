#!/usr/bin/env python3
"""
Script para probar que los archivos de audio se pueden leer correctamente
"""
import numpy as np
import scipy.io.wavfile as wav

def test_audio_file(filename):
    try:
        sample_rate, data = wav.read(filename)
        print(f"✅ {filename}:")
        print(f"   Sample rate: {sample_rate} Hz")
        print(f"   Duration: {len(data) / sample_rate:.2f} seconds")
        print(f"   Data type: {data.dtype}")
        print(f"   Shape: {data.shape}")
        print(f"   Min/Max values: {data.min():.6f} / {data.max():.6f}")
        return True
    except Exception as e:
        print(f"❌ Error reading {filename}: {e}")
        return False

if __name__ == "__main__":
    print("=== PROBANDO ARCHIVOS DE AUDIO ===\n")
    
    files_to_test = [
        "salida_con_pausas.wav",
        "salida_oraciones.wav", 
        "salida_con_pausas_16bit.wav",
        "salida_oraciones_16bit.wav"
    ]
    
    for filename in files_to_test:
        test_audio_file(filename)
        print()
    
    print("=== RESUMEN ===")
    print("Los archivos con '_16bit' deberían ser más compatibles con reproductores.")
    print("Ambos enfoques solucionan el problema de falta de pausas:")
    print("1. 'con_pausas' - Reemplaza puntuación con tokens de pausa")
    print("2. 'oraciones' - Divide en oraciones y agrega silencios entre ellas")
