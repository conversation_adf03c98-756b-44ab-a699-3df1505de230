import torch
from transformers import VitsModel, AutoTokenizer
from safetensors.torch import load_file
import scipy.io.wavfile as wav
import re
import numpy as np

# Rutas
BASE_MODEL = "ylacombe/mms-tts-spa-train"   # modelo base
CHECKPOINT_DIR = "/media/steve/Nuevo vol/Andrea_voz/backup_modelo_150_epocas"  # carpeta donde está model.safetensors
OUTPUT_WAV = "salida.wav"

def preprocess_text_for_tts(text):
    # Reemplazar puntuación con pausas
    text = re.sub(r'[,;]', ' <pause> ', text)  # pausas cortas
    text = re.sub(r'[.!?]', ' <long_pause> ', text)  # pausas largas
    text = re.sub(r'\s+', ' ', text)  # limpiar espacios múltiples
    return text.strip()

def generate_audio_with_sentences(model, tokenizer, text, device):
    # Dividir en oraciones
    sentences = re.split(r'[.!?]+', text)
    sentences = [s.strip() for s in sentences if s.strip()]
    
    audio_segments = []
    sample_rate = model.config.sampling_rate
    
    for sentence in sentences:
        inputs = tokenizer(text=sentence, return_tensors="pt").to(device)
        with torch.no_grad():
            output = model(**inputs)
            audio_segment = output.waveform.squeeze().cpu().numpy()
            audio_segments.append(audio_segment)
            
        # Agregar pausa entre oraciones (0.5 segundos de silencio)
        silence = np.zeros(int(sample_rate * 0.5))
        audio_segments.append(silence)
    
    return np.concatenate(audio_segments)

# 1. Cargar modelo base y tokenizer
model = VitsModel.from_pretrained(BASE_MODEL)
tokenizer = AutoTokenizer.from_pretrained(BASE_MODEL)

# 2. Cargar pesos fine-tuneados
checkpoint = load_file(f"{CHECKPOINT_DIR}/model.safetensors")
model.load_state_dict(checkpoint, strict=False)

# 3. Mandar el modelo a GPU si está disponible
device = "cuda" if torch.cuda.is_available() else "cpu"
model = model.to(device)

# 4. Texto de prueba - ENFOQUE 1: Con tokens de pausa
texto_original = "hola muy buenas tardes, le habla yatsuri yamilet de la mesa de servicios de solla, en que puedo ayudarle"
texto_con_pausas = preprocess_text_for_tts(texto_original)

print("Generando audio con tokens de pausa...")
inputs = tokenizer(text=texto_con_pausas, return_tensors="pt").to(device)
with torch.no_grad():
    output = model(**inputs)
    audio1 = output.waveform.squeeze().cpu().numpy()

wav.write("salida_con_pausas.wav", rate=model.config.sampling_rate, data=audio1)

# ENFOQUE 2: Dividir por oraciones
print("Generando audio dividido por oraciones...")
texto_oraciones = "hola muy buenas tardes. le habla yatsuri yamilet de la mesa de servicios de solla. en que puedo ayudarle."
audio2 = generate_audio_with_sentences(model, tokenizer, texto_oraciones, device)

wav.write("salida_oraciones.wav", rate=model.config.sampling_rate, data=audio2)

print("✅ Audio generado:")
print("  - salida_con_pausas.wav (con tokens de pausa)")
print("  - salida_oraciones.wav (dividido por oraciones)")