#!/usr/bin/env python3
"""
Script simplificado para TTS con pausas
Basado en el script andrea.py
"""
import torch
import re
import numpy as np
import warnings
warnings.filterwarnings("ignore")

from transformers import VitsModel, AutoTokenizer
from safetensors.torch import load_file
import scipy.io.wavfile as wav

def preprocess_text_for_tts(text):
    """
    ENFOQUE 1: Reemplazar puntuación con tokens de pausa
    """
    text = re.sub(r'[,;]', ' <pause> ', text)  # pausas cortas
    text = re.sub(r'[.!?]', ' <long_pause> ', text)  # pausas largas
    text = re.sub(r'\s+', ' ', text)  # limpiar espacios múltiples
    return text.strip()

def generate_audio_with_sentences(model, tokenizer, text, device):
    """
    ENFOQUE 2: Dividir en oraciones y agregar silencios
    """
    sentences = re.split(r'[.!?]+', text)
    sentences = [s.strip() for s in sentences if s.strip()]
    
    audio_segments = []
    sample_rate = model.config.sampling_rate
    
    for i, sentence in enumerate(sentences):
        print(f"  Procesando: '{sentence}'")
        inputs = tokenizer(text=sentence, return_tensors="pt").to(device)
        with torch.no_grad():
            output = model(**inputs)
            audio_segment = output.waveform.squeeze().cpu().numpy()
            audio_segments.append(audio_segment.astype(np.float32))
            
        # Agregar pausa entre oraciones
        if i < len(sentences) - 1:
            # Opciones de pausa (descomenta la que prefieras):
            silence = np.zeros(int(sample_rate * 0.05), dtype=np.float32)  # 50ms - muy corta
            # silence = np.zeros(int(sample_rate * 0.1), dtype=np.float32)   # 100ms - corta
            # silence = np.zeros(int(sample_rate * 0.2), dtype=np.float32)   # 200ms - normal
            # silence = np.zeros(int(sample_rate * 0.3), dtype=np.float32)   # 300ms - larga
            audio_segments.append(silence)
    
    return np.concatenate(audio_segments).astype(np.float32)

def main():
    # Configuración
    BASE_MODEL = "ylacombe/mms-tts-spa-train"
    CHECKPOINT_DIR = "/media/steve/Nuevo vol/Andrea_voz/backup_modelo_150_epocas"
    
    # Cargar modelo
    print("Cargando modelo...")
    model = VitsModel.from_pretrained(BASE_MODEL)
    tokenizer = AutoTokenizer.from_pretrained(BASE_MODEL)
    checkpoint = load_file(f"{CHECKPOINT_DIR}/model.safetensors")
    model.load_state_dict(checkpoint, strict=False)
    
    device = "cuda" if torch.cuda.is_available() else "cpu"
    model = model.to(device)
    print(f"Modelo cargado en: {device}")
    
    # Tu texto aquí
    texto = "Hola buenos días. ¿Cómo está usted? Espero que muy bien. me llamo andres felipe velez de la mesa de ayuda suramericana, en que puedo ayudarle hoy?"

    # ENFOQUE 2: Por oraciones
    print("\n=== ENFOQUE 2: Por oraciones ===")
    audio2 = generate_audio_with_sentences(model, tokenizer, texto, device)
    wav.write("mi_audio_oraciones.wav", rate=model.config.sampling_rate, data=audio2)
    print("✅ Guardado: mi_audio_oraciones.wav")

if __name__ == "__main__":
    main()
