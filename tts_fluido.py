#!/usr/bin/env python3
"""
TTS más fluido - procesa todo el texto junto pero mejora la puntuación
"""
import torch
import re
import numpy as np
import warnings
warnings.filterwarnings("ignore")

from transformers import VitsModel, AutoTokenizer
from safetensors.torch import load_file
import scipy.io.wavfile as wav

def mejorar_puntuacion(text):
    """
    Mejora la puntuación para que el modelo genere pausas más naturales
    """
    # Asegurar espacios después de puntuación
    text = re.sub(r'([.!?])([A-ZÁÉÍÓÚÑ])', r'\1 \2', text)  # Espacio después de punto
    text = re.sub(r'([,;])([A-Za-záéíóúñ])', r'\1 \2', text)  # Espacio después de coma
    
    # Normalizar espacios múltiples
    text = re.sub(r'\s+', ' ', text)
    
    return text.strip()

def generar_audio_fluido(model, tokenizer, text, device):
    """
    Genera audio procesando todo el texto junto (más fluido)
    """
    # Mejorar puntuación
    text_mejorado = mejorar_puntuacion(text)
    print(f"Texto procesado: {text_mejorado}")
    
    # Generar audio de todo el texto junto
    inputs = tokenizer(text=text_mejorado, return_tensors="pt").to(device)
    with torch.no_grad():
        output = model(**inputs)
        audio = output.waveform.squeeze().cpu().numpy()
    
    return audio.astype(np.float32)

def main():
    # Configuración
    BASE_MODEL = "ylacombe/mms-tts-spa-train"
    CHECKPOINT_DIR = "/media/steve/Nuevo vol/Andrea_voz/backup_modelo_150_epocas"
    
    # Cargar modelo
    print("Cargando modelo...")
    model = VitsModel.from_pretrained(BASE_MODEL)
    tokenizer = AutoTokenizer.from_pretrained(BASE_MODEL)
    checkpoint = load_file(f"{CHECKPOINT_DIR}/model.safetensors")
    model.load_state_dict(checkpoint, strict=False)
    
    device = "cuda" if torch.cuda.is_available() else "cpu"
    model = model.to(device)
    print(f"Modelo cargado en: {device}")
    
    # Tu texto
    texto = "Hola buenos días. ¿Cómo está usted? Espero que muy bien. Me llamo Andrés Felipe Vélez de la mesa de ayuda suramericana, ¿en qué puedo ayudarle hoy?"
    
    print("\n=== GENERANDO AUDIO FLUIDO ===")
    audio = generar_audio_fluido(model, tokenizer, texto, device)
    
    wav.write("audio_fluido.wav", rate=model.config.sampling_rate, data=audio)
    print("✅ Guardado: audio_fluido.wav")
    
    # Información del audio
    duracion = len(audio) / model.config.sampling_rate
    print(f"Duración: {duracion:.2f} segundos")

if __name__ == "__main__":
    main()
