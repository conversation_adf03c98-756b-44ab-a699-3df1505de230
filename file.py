"""
Usage:
1.
    Install uv from https://docs.astral.sh/uv/getting-started/installation
2.
    Copy this file to new folder
3.
    Download these files
    https://github.com/thewh1teagle/kokoro-onnx/releases/download/model-files-v1.0/kokoro-v1.0.onnx
    https://github.com/thewh1teagle/kokoro-onnx/releases/download/model-files-v1.0/voices-v1.0.bin
4. Run
    uv venv --seed -p 3.12
    source .venv/bin/activate
    uv pip install -U kokoro-onnx soundfile 'misaki[en]'
    uv run main.py

For other languages read https://huggingface.co/hexgrad/Kokoro-82M/blob/main/VOICES.md
"""

import soundfile as sf
import numpy as np
import re
from misaki import espeak
from misaki.espeak import EspeakG2P

from kokoro_onnx import Kokoro

def preprocess_text_with_pauses(text):
    """
    ENFOQUE 1: Reemplazar puntuación con marcadores de pausa
    """
    # Reemplazar puntuación con pausas
    text = re.sub(r'[,;]', ' <pausa_corta> ', text)  # pausas cortas para comas y punto y coma
    text = re.sub(r'[.!?]', ' <pausa_larga> ', text)  # pausas largas para puntos, exclamaciones, interrogaciones
    text = re.sub(r'\s+', ' ', text)  # limpiar espacios múltiples
    return text.strip()

def generate_audio_with_sentences(kokoro, g2p, text, voice="ef_dora"):
    """
    ENFOQUE 2: Dividir el texto en oraciones y agregar silencios entre ellas
    """
    # Dividir en oraciones
    sentences = re.split(r'[.!?]+', text)
    sentences = [s.strip() for s in sentences if s.strip()]

    audio_segments = []
    sample_rate = None

    for i, sentence in enumerate(sentences):
        print(f"Procesando oración {i+1}: {sentence}")

        # Convertir a fonemas
        phonemes, _ = g2p(sentence)

        # Generar audio para la oración
        samples, sr = kokoro.create(phonemes, voice, is_phonemes=True)

        if sample_rate is None:
            sample_rate = sr

        audio_segments.append(samples)

        # Agregar pausa entre oraciones (excepto la última)
        if i < len(sentences) - 1:
            # 0.8 segundos de silencio entre oraciones
            silence_duration = 0.8
            silence_samples = int(sample_rate * silence_duration)
            silence = np.zeros(silence_samples, dtype=samples.dtype)
            audio_segments.append(silence)

    # Concatenar todos los segmentos
    final_audio = np.concatenate(audio_segments)
    return final_audio, sample_rate

# Misaki G2P with espeak-ng fallback
fallback = espeak.EspeakFallback(british=False)
g2p = EspeakG2P(language="es")

# Kokoro
kokoro = Kokoro("kokoro-v1.0.onnx", "voices-v1.0.bin")

# Texto de prueba
text_original = "Hola, muy buenas tardes. Le habla Yatsuri Yamilet de la mesa de servicios de Solla. ¿En qué puedo ayudarle?"

print("=== GENERANDO AUDIO ORIGINAL (sin pausas) ===")
# Phonemize texto original
phonemes_original, _ = g2p(text_original)
# Create audio original
samples_original, sample_rate = kokoro.create(phonemes_original, "ef_dora", is_phonemes=True)
# Save audio original
sf.write("audio_original.wav", samples_original, sample_rate)
print("✅ Creado: audio_original.wav (sin pausas naturales)")

print("\n=== GENERANDO AUDIO CON TOKENS DE PAUSA (Enfoque 1) ===")
# Procesar texto con marcadores de pausa
text_con_pausas = preprocess_text_with_pauses(text_original)
print(f"Texto procesado: {text_con_pausas}")
# Phonemize texto con pausas
phonemes_pausas, _ = g2p(text_con_pausas)
# Create audio con pausas
samples_pausas, sample_rate = kokoro.create(phonemes_pausas, "ef_dora", is_phonemes=True)
# Save audio con pausas
sf.write("audio_con_pausas.wav", samples_pausas, sample_rate)
print("✅ Creado: audio_con_pausas.wav (con tokens de pausa)")

print("\n=== GENERANDO AUDIO POR ORACIONES (Enfoque 2) ===")
# Generar audio dividiendo por oraciones
samples_oraciones, sample_rate = generate_audio_with_sentences(kokoro, g2p, text_original, "ef_dora")
# Save audio por oraciones
sf.write("audio_oraciones.wav", samples_oraciones, sample_rate)
print("✅ Creado: audio_oraciones.wav (dividido por oraciones con silencios)")

print("\n🎵 ARCHIVOS GENERADOS:")
print("  1. audio_original.wav - Sin pausas (problema original)")
print("  2. audio_con_pausas.wav - Con tokens de pausa")
print("  3. audio_oraciones.wav - Dividido por oraciones con silencios")
print("\n🎧 Reproduce los archivos para escuchar la diferencia!")